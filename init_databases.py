#!/usr/bin/env python3
"""
数据库初始化脚本
用于初始化SQLite和ChromaDB数据库
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_chroma_db():
    """初始化ChromaDB数据库"""
    try:
        import chromadb
        from backend.config import settings

        logger.info("开始初始化ChromaDB...")
        logger.info(f"ChromaDB版本: {chromadb.__version__}")
        logger.info(f"存储目录: {settings.chroma_persist_directory}")
        logger.info(f"集合名称: {settings.collection_name}")

        # 确保存储目录存在
        storage_path = Path(settings.chroma_persist_directory)
        storage_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"存储目录已创建: {storage_path.absolute()}")

        # 创建ChromaDB客户端
        client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory
        )
        logger.info("ChromaDB客户端创建成功")

        # 创建或获取集合，确保支持1536维向量
        try:
            collection = client.get_collection(name=settings.collection_name)
            logger.info(f"找到现有集合: {settings.collection_name}")

            # 检查集合维度
            if collection.count() > 0:
                # 获取一个样本向量来检查维度
                sample = collection.get(limit=1, include=["embeddings"])
                if sample['embeddings']:
                    actual_dim = len(sample['embeddings'][0])
                    logger.info(f"现有集合向量维度: {actual_dim}")
                    if actual_dim != 1536:
                        logger.warning(f"集合维度不匹配！期望1536，实际{actual_dim}")
                        logger.info("建议重新创建集合以支持1536维向量")
            else:
                logger.info("集合为空，将在首次添加向量时确定维度")

        except Exception:
            # 创建新集合，使用余弦相似度
            collection = client.create_collection(
                name=settings.collection_name,
                metadata={"hnsw:space": "cosine"}  # 适合1536维向量的配置
            )
            logger.info(f"创建新集合: {settings.collection_name}")
            logger.info("集合配置: 余弦相似度，支持1536维向量")

        # 检查集合信息
        count = collection.count()
        logger.info(f"集合中的文档数量: {count}")
        logger.info(f"集合元数据: {collection.metadata}")

        return True

    except Exception as e:
        logger.error(f"ChromaDB初始化失败: {e}")
        return False

def check_sqlite_files():
    """检查SQLite数据库文件"""
    try:
        from backend.config import settings
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        logger.info("检查SQLite数据库文件:")
        if sqlite_files:
            for file in sqlite_files:
                size = file.stat().st_size
                logger.info(f"  - {file.name}: {size} bytes")
        else:
            logger.warning("  未找到SQLite数据库文件")
            
        return len(sqlite_files) > 0
        
    except Exception as e:
        logger.error(f"检查SQLite文件失败: {e}")
        return False

def verify_database_structure():
    """验证数据库结构"""
    try:
        import sqlite3
        from backend.config import settings

        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))

        if not sqlite_files:
            logger.warning("没有SQLite文件可供验证")
            return False

        db_file = sqlite_files[0]
        logger.info(f"验证数据库结构: {db_file}")

        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()

            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            logger.info("数据库表结构:")
            for table in tables:
                table_name = table[0]
                logger.info(f"  - {table_name}")

                # 获取表的列信息
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                for col in columns:
                    logger.info(f"    {col[1]} {col[2]}")

            # 检查集合维度配置
            cursor.execute("SELECT name, dimension FROM collections;")
            collections = cursor.fetchall()
            for name, dimension in collections:
                logger.info(f"集合 '{name}' 配置维度: {dimension}")
                if dimension == 1536:
                    logger.info("✅ 集合维度配置正确（1536维）")
                else:
                    logger.warning(f"⚠️ 集合维度配置异常: {dimension}，期望1536")

        return True

    except Exception as e:
        logger.error(f"验证数据库结构失败: {e}")
        return False

def test_1536_dimension():
    """测试1536维向量功能"""
    try:
        import chromadb
        import numpy as np
        from backend.config import settings

        logger.info("测试1536维向量功能...")

        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(name=settings.collection_name)

        # 生成1536维测试向量
        test_embedding = np.random.random(1536).tolist()
        test_id = "dimension_test_1536"

        # 添加测试向量
        collection.add(
            embeddings=[test_embedding],
            documents=["1536维向量测试文档"],
            metadatas=[{"test": "dimension_check", "dimension": 1536}],
            ids=[test_id]
        )
        logger.info("✅ 成功添加1536维测试向量")

        # 验证向量维度
        results = collection.get(ids=[test_id], include=["embeddings"])
        if results['embeddings'] and len(results['embeddings']) > 0:
            actual_dim = len(results['embeddings'][0])
            logger.info(f"验证结果: 实际向量维度 = {actual_dim}")

            if actual_dim == 1536:
                logger.info("✅ 1536维向量测试通过")
                success = True
            else:
                logger.error(f"❌ 维度不匹配: 期望1536，实际{actual_dim}")
                success = False
        else:
            logger.error("❌ 无法获取测试向量")
            success = False

        # 清理测试数据
        collection.delete(ids=[test_id])
        logger.info("测试数据已清理")

        return success

    except Exception as e:
        logger.error(f"1536维向量测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始数据库初始化")
    logger.info("=" * 50)

    # 步骤1: 初始化ChromaDB
    logger.info("\n步骤1: 初始化ChromaDB")
    chroma_success = init_chroma_db()

    # 步骤2: 检查SQLite文件
    logger.info("\n步骤2: 检查SQLite文件")
    sqlite_success = check_sqlite_files()

    # 步骤3: 验证数据库结构
    logger.info("\n步骤3: 验证数据库结构")
    structure_success = verify_database_structure()

    # 步骤4: 测试1536维向量功能
    logger.info("\n步骤4: 测试1536维向量功能")
    dimension_success = test_1536_dimension()

    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("初始化结果总结:")
    logger.info(f"  ChromaDB初始化: {'✅ 成功' if chroma_success else '❌ 失败'}")
    logger.info(f"  SQLite文件检查: {'✅ 成功' if sqlite_success else '❌ 失败'}")
    logger.info(f"  数据库结构验证: {'✅ 成功' if structure_success else '❌ 失败'}")
    logger.info(f"  1536维向量测试: {'✅ 成功' if dimension_success else '❌ 失败'}")

    if chroma_success and sqlite_success and dimension_success:
        logger.info("🎉 数据库初始化完成! 支持1536维向量")
        return True
    else:
        logger.error("❌ 数据库初始化失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
