#!/usr/bin/env python3
"""
检查ChromaDB中的字段名和数据结构
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

import chromadb
import sqlite3
import json
from backend.config import settings

def check_chroma_fields():
    """检查ChromaDB中的字段名和数据结构"""
    try:
        print("=" * 60)
        print("检查ChromaDB字段名和数据结构")
        print("=" * 60)
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(name=settings.collection_name)
        
        print(f"集合名称: {settings.collection_name}")
        print(f"文档总数: {collection.count()}")
        
        if collection.count() == 0:
            print("⚠️ 集合为空")
            return
        
        # 获取前几个文档的详细信息
        result = collection.get(
            limit=3,
            include=["documents", "metadatas", "embeddings"]
        )
        
        print(f"\n📊 获取到 {len(result['ids'])} 个文档")
        
        for i, doc_id in enumerate(result['ids']):
            print(f"\n--- 文档 {i+1} ---")
            print(f"ID: {doc_id}")
            
            # 检查文档内容
            if 'documents' in result and i < len(result['documents']):
                doc_content = result['documents'][i]
                print(f"文档内容类型: {type(doc_content)}")
                if doc_content:
                    print(f"文档内容长度: {len(doc_content)}")
                    print(f"文档内容前100字符: {doc_content[:100]}...")
                else:
                    print("⚠️ 文档内容为空或None")
            
            # 检查元数据
            if 'metadatas' in result and i < len(result['metadatas']):
                metadata = result['metadatas'][i]
                print(f"元数据: {metadata}")
            
            # 检查向量
            if 'embeddings' in result and i < len(result['embeddings']):
                embedding = result['embeddings'][i]
                if embedding is not None and len(embedding) > 0:
                    print(f"向量维度: {len(embedding)}")
                else:
                    print("⚠️ 向量为空")
        
        # 直接查看SQLite数据库
        print(f"\n" + "=" * 60)
        print("直接检查SQLite数据库")
        print("=" * 60)
        
        db_path = Path(settings.chroma_persist_directory) / "chroma.sqlite3"
        if db_path.exists():
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # 查看表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"数据库表: {[table[0] for table in tables]}")
            
            # 查看embedding_metadata表结构
            if any('embedding_metadata' in table for table in tables):
                cursor.execute("PRAGMA table_info(embedding_metadata);")
                columns = cursor.fetchall()
                print(f"\nembedding_metadata表结构:")
                for col in columns:
                    print(f"  {col[1]} ({col[2]})")
                
                # 查看前几条记录
                cursor.execute("SELECT * FROM embedding_metadata LIMIT 3;")
                rows = cursor.fetchall()
                print(f"\nembedding_metadata前3条记录:")
                for i, row in enumerate(rows):
                    print(f"  记录 {i+1}: {row}")
            
            conn.close()
        else:
            print(f"⚠️ SQLite数据库文件不存在: {db_path}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_chroma_fields()
