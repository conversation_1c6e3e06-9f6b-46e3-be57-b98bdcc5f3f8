#!/usr/bin/env python3
"""
检查 ChromaDB embedding_metadata 表的数据问题
"""
import sqlite3
import json
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_metadata_problem():
    """检查元数据存储问题"""
    db_path = Path("storage/chroma.sqlite3")
    
    if not db_path.exists():
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            logger.info("=== 检查 embedding_metadata 表结构 ===")
            cursor.execute("PRAGMA table_info(embedding_metadata);")
            columns = cursor.fetchall()
            for col in columns:
                logger.info(f"列: {col}")
            
            # 检查数据总数
            cursor.execute("SELECT COUNT(*) FROM embedding_metadata;")
            total_count = cursor.fetchone()[0]
            logger.info(f"\n=== embedding_metadata 表总记录数: {total_count} ===")
            
            if total_count == 0:
                logger.warning("表为空！")
                return True
            
            # 检查前几条记录
            logger.info("\n=== 检查前10条记录 ===")
            cursor.execute("SELECT id, key, string_value FROM embedding_metadata LIMIT 10;")
            records = cursor.fetchall()
            
            problem_found = False
            for record_id, key, string_value in records:
                logger.info(f"\nID: {record_id}")
                logger.info(f"Key: {key}")
                
                # 检查 string_value 是否是 JSON
                if string_value and len(string_value) > 100:
                    logger.warning(f"⚠️ 可能的问题：string_value 过长 ({len(string_value)} 字符)")
                    
                    # 尝试解析为 JSON
                    try:
                        parsed = json.loads(string_value)
                        logger.error(f"❌ 发现问题：string_value 是完整的 JSON 对象！")
                        logger.error(f"JSON 类型: {type(parsed)}")
                        if isinstance(parsed, dict):
                            logger.error(f"JSON 键: {list(parsed.keys())[:5]}...")  # 只显示前5个键
                        problem_found = True
                    except json.JSONDecodeError:
                        logger.info(f"✅ string_value 不是 JSON，长度: {len(string_value)}")
                        logger.info(f"前100字符: {string_value[:100]}...")
                else:
                    logger.info(f"✅ string_value 正常: {string_value}")
            
            # 统计不同的 key 类型
            logger.info("\n=== 统计 key 类型分布 ===")
            cursor.execute("SELECT key, COUNT(*) as count FROM embedding_metadata GROUP BY key ORDER BY count DESC;")
            key_stats = cursor.fetchall()
            for key, count in key_stats:
                logger.info(f"Key '{key}': {count} 条记录")
            
            # 检查是否有异常的 key
            expected_keys = {'filename', 'file_path', 'file_size', 'file_modified', 'redirect_url', 'content_url'}
            actual_keys = {key for key, _ in key_stats}
            unexpected_keys = actual_keys - expected_keys
            
            if unexpected_keys:
                logger.warning(f"⚠️ 发现异常的 key: {unexpected_keys}")
                problem_found = True
            
            # 检查 embeddings 表
            logger.info("\n=== 检查 embeddings 表 ===")
            cursor.execute("SELECT COUNT(*) FROM embeddings;")
            embeddings_count = cursor.fetchone()[0]
            logger.info(f"embeddings 表记录数: {embeddings_count}")
            
            if embeddings_count > 0:
                cursor.execute("SELECT id, embedding_id, segment_id FROM embeddings LIMIT 5;")
                embeddings = cursor.fetchall()
                for emb_id, embedding_id, segment_id in embeddings:
                    logger.info(f"Embedding ID: {emb_id}, embedding_id: {embedding_id}, segment_id: {segment_id}")
            
            return not problem_found
            
    except Exception as e:
        logger.error(f"检查数据库失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def analyze_problem_cause():
    """分析问题原因"""
    logger.info("\n" + "="*60)
    logger.info("问题原因分析")
    logger.info("="*60)
    
    logger.info("可能的原因：")
    logger.info("1. LlamaIndex 在存储元数据时出现序列化错误")
    logger.info("2. ChromaVectorStore 的元数据处理逻辑有问题")
    logger.info("3. 文档节点的元数据被错误地序列化为 JSON 字符串")
    logger.info("4. 可能是 LlamaIndex 版本兼容性问题")

def propose_solution():
    """提出解决方案"""
    logger.info("\n" + "="*60)
    logger.info("解决方案")
    logger.info("="*60)
    
    logger.info("方案1: 清理并重建数据")
    logger.info("  - 删除所有现有的向量数据")
    logger.info("  - 重新上传文档")
    logger.info("  - 优点: 彻底解决问题")
    logger.info("  - 缺点: 需要重新处理所有文档")
    
    logger.info("\n方案2: 修复现有数据")
    logger.info("  - 解析 JSON 数据，提取正确的元数据值")
    logger.info("  - 更新数据库记录")
    logger.info("  - 优点: 保留现有数据")
    logger.info("  - 缺点: 复杂，可能不完整")
    
    logger.info("\n方案3: 检查代码并修复根本原因")
    logger.info("  - 找到导致问题的代码位置")
    logger.info("  - 修复元数据存储逻辑")
    logger.info("  - 然后清理数据重新开始")

if __name__ == "__main__":
    logger.info("开始检查 ChromaDB 元数据问题...")
    
    success = check_metadata_problem()
    
    if not success:
        analyze_problem_cause()
        propose_solution()
    else:
        logger.info("✅ 数据库状态正常，未发现问题")
