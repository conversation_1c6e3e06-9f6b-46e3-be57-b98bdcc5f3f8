#!/usr/bin/env python3
"""
调试ChromaDB数据结构的脚本
"""
import json
import chromadb
from pathlib import Path
import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.config import settings

def debug_chromadb():
    """调试ChromaDB中的数据结构"""
    try:
        print("=== ChromaDB 数据结构调试 ===")
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        print(f"ChromaDB路径: {settings.chroma_persist_directory}")
        
        # 获取集合
        try:
            collection = client.get_collection(name=settings.collection_name)
            print(f"集合名称: {settings.collection_name}")
        except Exception as e:
            print(f"获取集合失败: {e}")
            return
        
        # 获取集合统计信息
        count = collection.count()
        print(f"文档总数: {count}")
        
        if count == 0:
            print("集合为空，没有数据可调试")
            return
        
        # 获取前5个文档的完整信息
        result = collection.get(
            limit=5,
            include=["metadatas", "documents", "embeddings"]
        )
        
        print(f"\n=== 前5个文档的数据结构 ===")
        
        for i in range(min(5, len(result["ids"]))):
            print(f"\n--- 文档 {i+1} ---")
            print(f"ID: {result['ids'][i]}")
            
            # 检查元数据
            if result["metadatas"] and i < len(result["metadatas"]):
                metadata = result["metadatas"][i]
                print(f"元数据类型: {type(metadata)}")
                print(f"元数据内容: {metadata}")
                
                # 如果是字符串，尝试解析JSON
                if isinstance(metadata, str):
                    try:
                        parsed_metadata = json.loads(metadata)
                        print(f"解析后的元数据: {parsed_metadata}")
                        
                        # 检查文件名编码
                        if "filename" in parsed_metadata:
                            filename = parsed_metadata["filename"]
                            print(f"文件名原始: {filename}")
                            print(f"文件名类型: {type(filename)}")
                            
                            # 尝试解码Unicode
                            if isinstance(filename, str) and "\\u" in filename:
                                try:
                                    decoded_filename = filename.encode().decode('unicode_escape')
                                    print(f"解码后文件名: {decoded_filename}")
                                except Exception as e:
                                    print(f"解码文件名失败: {e}")
                        
                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                elif isinstance(metadata, dict):
                    print("元数据是字典格式")
                    for key, value in metadata.items():
                        print(f"  {key}: {value} (类型: {type(value)})")
            
            # 检查文档内容
            if result["documents"] and i < len(result["documents"]):
                doc = result["documents"][i]
                print(f"文档内容长度: {len(doc) if doc else 0}")
                if doc:
                    print(f"文档内容预览: {doc[:100]}...")
            
            # 检查嵌入向量
            if result["embeddings"] and i < len(result["embeddings"]):
                embedding = result["embeddings"][i]
                print(f"嵌入向量维度: {len(embedding) if embedding else 0}")
        
        print(f"\n=== 所有文档的文件名统计 ===")
        
        # 获取所有元数据
        all_result = collection.get(include=["metadatas"])
        filenames = set()
        
        for metadata in all_result["metadatas"]:
            if isinstance(metadata, str):
                try:
                    parsed = json.loads(metadata)
                    if "filename" in parsed:
                        filename = parsed["filename"]
                        # 尝试解码Unicode
                        if isinstance(filename, str) and "\\u" in filename:
                            try:
                                decoded_filename = filename.encode().decode('unicode_escape')
                                filenames.add(decoded_filename)
                            except:
                                filenames.add(filename)
                        else:
                            filenames.add(filename)
                except:
                    pass
            elif isinstance(metadata, dict) and "filename" in metadata:
                filenames.add(metadata["filename"])
        
        print(f"唯一文件名数量: {len(filenames)}")
        for filename in sorted(filenames):
            print(f"  - {filename}")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_chromadb()
