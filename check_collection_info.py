#!/usr/bin/env python3
"""
检查ChromaDB集合信息
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

import chromadb
import sqlite3
from backend.config import settings

def check_collection_info():
    """检查集合详细信息"""
    try:
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(name=settings.collection_name)
        
        print("=== ChromaDB集合信息 ===")
        print(f"集合名称: {collection.name}")
        print(f"集合ID: {collection.id}")
        print(f"文档数量: {collection.count()}")
        print(f"元数据: {collection.metadata}")
        
        # 检查SQLite中的集合信息
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if sqlite_files:
            db_file = sqlite_files[0]
            with sqlite3.connect(db_file) as conn:
                cursor = conn.cursor()
                
                # 查询collections表
                cursor.execute("SELECT id, name, dimension, config_json_str FROM collections;")
                collections = cursor.fetchall()
                
                print("\n=== SQLite集合信息 ===")
                for col_id, name, dimension, config in collections:
                    print(f"ID: {col_id}")
                    print(f"名称: {name}")
                    print(f"维度: {dimension}")
                    print(f"配置: {config}")
                    
        return True
        
    except Exception as e:
        print(f"检查失败: {e}")
        return False

if __name__ == "__main__":
    check_collection_info()
