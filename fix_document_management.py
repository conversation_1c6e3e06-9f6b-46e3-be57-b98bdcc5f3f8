#!/usr/bin/env python3
"""
修复文档管理页面问题
清理所有数据并重新初始化
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clean_all_data():
    """清理所有ChromaDB数据"""
    try:
        import chromadb
        from backend.config import settings
        
        logger.info("=" * 60)
        logger.info("开始清理所有ChromaDB数据")
        logger.info("=" * 60)
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        logger.info("✅ ChromaDB客户端连接成功")
        
        # 删除现有集合
        try:
            client.delete_collection(name=settings.collection_name)
            logger.info(f"✅ 已删除集合: {settings.collection_name}")
        except Exception as e:
            logger.warning(f"⚠️ 删除集合时出现错误（可能集合不存在）: {e}")
        
        # 重新创建集合，确保1536维
        collection = client.create_collection(
            name=settings.collection_name,
            metadata={"hnsw:space": "cosine"}
        )
        logger.info(f"✅ 重新创建集合: {settings.collection_name}")
        logger.info(f"集合ID: {collection.id}")
        logger.info(f"集合元数据: {collection.metadata}")
        
        # 验证集合为空
        count = collection.count()
        logger.info(f"✅ 集合文档数量: {count} (应该为0)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 清理数据失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def verify_clean_state():
    """验证清理后的状态"""
    try:
        import sqlite3
        from backend.config import settings
        
        logger.info("\n" + "=" * 60)
        logger.info("验证清理后的数据库状态")
        logger.info("=" * 60)
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.error("❌ 未找到SQLite数据库文件")
            return False
            
        db_file = sqlite_files[0]
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查关键表
            tables_to_check = ['embeddings', 'embedding_metadata']
            
            for table_name in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                logger.info(f"📋 {table_name}: {count} 行")
                
                if count == 0:
                    logger.info(f"✅ {table_name}表已清空")
                else:
                    logger.warning(f"⚠️ {table_name}表仍有数据: {count}行")
            
            # 检查集合配置
            cursor.execute("SELECT name, dimension FROM collections;")
            collections = cursor.fetchall()
            for name, dimension in collections:
                logger.info(f"📊 集合: {name}, 维度: {dimension}")
                if dimension == 1536:
                    logger.info("✅ 集合维度配置正确（1536维）")
                else:
                    logger.warning(f"⚠️ 集合维度异常: {dimension}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证状态失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_document_upload():
    """测试文档上传功能"""
    try:
        from backend.app.rag_service import RAGService
        
        logger.info("\n" + "=" * 60)
        logger.info("测试文档上传功能")
        logger.info("=" * 60)
        
        # 初始化RAG服务
        logger.info("初始化RAG服务...")
        rag_service = RAGService()
        
        # 创建测试文档
        test_content = """
这是一个测试文档，用于验证文档管理页面修复。

测试内容：
1. 文档上传功能
2. 元数据存储
3. 文档列表获取
4. 1536维向量生成

修复后的系统应该能够：
- 正确显示文档数量
- 获取文档列表
- 进行向量检索
- 管理文档元数据
        """
        
        # 上传测试文档
        logger.info("上传测试文档...")
        result = rag_service.upload_document(
            file_content=test_content,
            filename="test_fix_document.txt"
        )
        
        if result["success"]:
            logger.info("✅ 测试文档上传成功!")
            logger.info(f"新文档块数: {result.get('new_chunks', 0)}")
            logger.info(f"总文档块数: {result.get('total_chunks', 0)}")
            
            # 测试查询
            logger.info("测试查询功能...")
            query_result = rag_service.query("文档管理页面", max_results=1)
            if query_result and query_result.get("success"):
                logger.info("✅ 查询功能正常")
            else:
                logger.warning("⚠️ 查询功能异常")
                
            return True
        else:
            logger.error(f"❌ 测试文档上传失败: {result.get('message', '未知错误')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试上传功能失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def verify_final_state():
    """验证最终状态"""
    try:
        import sqlite3
        from backend.config import settings
        
        logger.info("\n" + "=" * 60)
        logger.info("验证最终修复状态")
        logger.info("=" * 60)
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        db_file = sqlite_files[0]
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查数据一致性
            cursor.execute("SELECT COUNT(*) FROM embeddings;")
            embedding_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM embedding_metadata;")
            metadata_count = cursor.fetchone()[0]
            
            logger.info(f"📊 向量数据: {embedding_count} 条")
            logger.info(f"📊 元数据: {metadata_count} 条")
            
            if embedding_count > 0 and metadata_count > 0:
                logger.info("✅ 数据一致性正常")
                
                # 检查filename字段
                cursor.execute("SELECT DISTINCT string_value FROM embedding_metadata WHERE key='filename';")
                filenames = cursor.fetchall()
                logger.info(f"📄 文档文件名: {[f[0] for f in filenames]}")
                
                return True
            else:
                logger.error("❌ 数据一致性异常")
                return False
        
    except Exception as e:
        logger.error(f"❌ 验证最终状态失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔧 开始修复文档管理页面问题")
    
    # 步骤1: 清理所有数据
    logger.info("\n步骤1: 清理所有数据")
    clean_success = clean_all_data()
    
    # 步骤2: 验证清理状态
    logger.info("\n步骤2: 验证清理状态")
    verify_success = verify_clean_state()
    
    # 步骤3: 测试文档上传
    logger.info("\n步骤3: 测试文档上传")
    upload_success = test_document_upload()
    
    # 步骤4: 验证最终状态
    logger.info("\n步骤4: 验证最终状态")
    final_success = verify_final_state()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("修复结果总结")
    logger.info("=" * 60)
    logger.info(f"数据清理: {'✅ 成功' if clean_success else '❌ 失败'}")
    logger.info(f"状态验证: {'✅ 成功' if verify_success else '❌ 失败'}")
    logger.info(f"上传测试: {'✅ 成功' if upload_success else '❌ 失败'}")
    logger.info(f"最终验证: {'✅ 成功' if final_success else '❌ 失败'}")
    
    if all([clean_success, verify_success, upload_success, final_success]):
        logger.info("🎉 文档管理页面问题修复完成!")
        logger.info("现在可以正常使用文档管理功能了")
    else:
        logger.error("❌ 修复过程中出现问题，请检查错误信息")
    
    return all([clean_success, verify_success, upload_success, final_success])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
