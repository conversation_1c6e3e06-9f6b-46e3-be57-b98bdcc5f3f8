"""
Pydantic模型定义
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

class ArticleBase(BaseModel):
    """文章基础模型"""
    title: str = Field(..., description="文章标题")
    content: Optional[str] = Field(None, description="文章内容")
    summary: Optional[str] = Field(None, description="文章摘要")
    author: Optional[str] = Field(None, description="作者")
    category: Optional[str] = Field(None, description="分类")
    tags: Optional[str] = Field(None, description="标签")
    status: str = Field("published", description="状态")
    source_url: Optional[str] = Field(None, description="来源URL")

class ArticleCreate(ArticleBase):
    """创建文章模型"""
    pass

class ArticleUpdate(BaseModel):
    """更新文章模型"""
    title: Optional[str] = None
    content: Optional[str] = None
    summary: Optional[str] = None
    author: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    status: Optional[str] = None
    source_url: Optional[str] = None

class ArticleResponse(ArticleBase):
    """文章响应模型"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    published_at: Optional[datetime] = None
    is_deleted: bool = False
    
    class Config:
        from_attributes = True

class ArticleListResponse(BaseModel):
    """文章列表响应模型"""
    articles: List[ArticleResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

class SyncRequest(BaseModel):
    """同步请求模型"""
    force: bool = Field(False, description="是否强制同步")
    limit: Optional[int] = Field(None, description="限制同步数量")

class SyncResponse(BaseModel):
    """同步响应模型"""
    success: bool
    message: str
    added_count: int = 0
    updated_count: int = 0
    deleted_count: int = 0
    error_count: int = 0
    details: Optional[Dict[str, Any]] = None

class DatabaseStatus(BaseModel):
    """数据库状态模型"""
    connected: bool
    message: str
    article_count: Optional[int] = None

class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    database: DatabaseStatus
    timestamp: datetime
