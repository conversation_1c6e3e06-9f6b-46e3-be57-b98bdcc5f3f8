#!/usr/bin/env python3
"""
重新创建ChromaDB集合，使用正确的1536维度
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

import chromadb
from backend.config import settings

def recreate_collection():
    """重新创建集合，使用1536维度"""
    try:
        print("开始重新创建ChromaDB集合...")
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        
        # 删除现有集合
        try:
            client.delete_collection(name=settings.collection_name)
            print(f"✅ 已删除现有集合: {settings.collection_name}")
        except Exception as e:
            print(f"⚠️ 删除集合时出现错误（可能集合不存在）: {e}")
        
        # 创建新集合，明确指定1536维度的嵌入函数
        collection = client.create_collection(
            name=settings.collection_name,
            metadata={"hnsw:space": "cosine"}  # 使用余弦相似度
        )
        
        print(f"✅ 已创建新集合: {settings.collection_name}")
        print(f"集合ID: {collection.id}")
        print(f"集合元数据: {collection.metadata}")
        
        # 测试添加一个1536维的向量
        import numpy as np
        test_embedding = np.random.random(1536).tolist()
        
        collection.add(
            embeddings=[test_embedding],
            documents=["测试文档，用于验证1536维向量"],
            metadatas=[{"test": "true", "dimension": 1536}],
            ids=["test_1536_dim"]
        )
        
        print("✅ 成功添加1536维测试向量")
        print(f"集合文档数量: {collection.count()}")
        
        # 验证向量维度
        results = collection.get(ids=["test_1536_dim"], include=["embeddings"])
        if results['embeddings']:
            actual_dim = len(results['embeddings'][0])
            print(f"✅ 验证成功，实际向量维度: {actual_dim}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重新创建集合失败: {e}")
        return False

if __name__ == "__main__":
    success = recreate_collection()
    if success:
        print("\n🎉 集合重新创建成功！现在支持1536维向量")
    else:
        print("\n❌ 集合重新创建失败")
    sys.exit(0 if success else 1)
