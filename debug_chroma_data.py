#!/usr/bin/env python3
"""
ChromaDB数据库调试脚本
输出所有数据用于诊断文档管理页面加载失败问题
"""
import os
import sys
import json
import sqlite3
import logging
from pathlib import Path
from typing import Dict, Any, List

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_chroma_collection():
    """调试ChromaDB集合数据"""
    try:
        import chromadb
        from backend.config import settings
        
        logger.info("=" * 60)
        logger.info("开始调试ChromaDB集合数据")
        logger.info("=" * 60)
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        logger.info(f"✅ ChromaDB客户端连接成功")
        logger.info(f"存储路径: {settings.chroma_persist_directory}")
        
        # 获取集合
        collection = client.get_collection(name=settings.collection_name)
        logger.info(f"✅ 获取集合成功: {settings.collection_name}")
        logger.info(f"集合ID: {collection.id}")
        logger.info(f"集合元数据: {collection.metadata}")
        
        # 获取文档总数
        total_count = collection.count()
        logger.info(f"📊 集合中总文档数: {total_count}")
        
        if total_count == 0:
            logger.warning("⚠️ 集合为空，没有文档数据")
            return True
        
        # 获取所有数据
        logger.info("\n🔍 获取所有文档数据...")
        all_data = collection.get(
            include=["documents", "metadatas", "embeddings"]
        )
        
        logger.info(f"实际获取到的文档数: {len(all_data.get('ids', []))}")
        
        # 详细输出每个文档
        for i, doc_id in enumerate(all_data.get('ids', [])):
            logger.info(f"\n📄 文档 {i+1}/{len(all_data['ids'])}")
            logger.info(f"  ID: {doc_id}")
            
            # 文档内容
            if all_data.get('documents') and i < len(all_data['documents']):
                doc_content = all_data['documents'][i]
                logger.info(f"  内容长度: {len(doc_content) if doc_content else 0}")
                if doc_content:
                    preview = doc_content[:200] + "..." if len(doc_content) > 200 else doc_content
                    logger.info(f"  内容预览: {preview}")
                else:
                    logger.warning(f"  ⚠️ 文档内容为空")
            
            # 元数据
            if all_data.get('metadatas') and i < len(all_data['metadatas']):
                metadata = all_data['metadatas'][i]
                logger.info(f"  元数据: {json.dumps(metadata, ensure_ascii=False, indent=4)}")
            else:
                logger.warning(f"  ⚠️ 元数据缺失")
            
            # 向量信息
            embeddings_list = all_data.get('embeddings')
            if embeddings_list is not None and i < len(embeddings_list):
                embedding = embeddings_list[i]
                if embedding is not None and len(embedding) > 0:
                    logger.info(f"  向量维度: {len(embedding)}")
                    logger.info(f"  向量前5个值: {embedding[:5]}")
                else:
                    logger.warning(f"  ⚠️ 向量数据为空")
            else:
                logger.warning(f"  ⚠️ 向量数据缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调试ChromaDB集合失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def debug_sqlite_data():
    """调试SQLite数据库数据"""
    try:
        from backend.config import settings
        
        logger.info("\n" + "=" * 60)
        logger.info("开始调试SQLite数据库数据")
        logger.info("=" * 60)
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.error("❌ 未找到SQLite数据库文件")
            return False
            
        db_file = sqlite_files[0]
        logger.info(f"📁 数据库文件: {db_file}")
        logger.info(f"📊 文件大小: {db_file.stat().st_size} bytes")
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查关键表的数据
            tables_to_check = [
                'collections', 'embeddings', 'embedding_metadata', 
                'segments', 'tenants', 'databases'
            ]
            
            for table_name in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                    count = cursor.fetchone()[0]
                    logger.info(f"📋 {table_name}: {count} 行")
                    
                    if table_name == 'collections' and count > 0:
                        cursor.execute("SELECT id, name, dimension FROM collections;")
                        collections = cursor.fetchall()
                        for col_id, name, dimension in collections:
                            logger.info(f"  集合: {name}, ID: {col_id}, 维度: {dimension}")
                    
                    elif table_name == 'embeddings' and count > 0:
                        cursor.execute("SELECT id, embedding_id, segment_id FROM embeddings LIMIT 5;")
                        embeddings = cursor.fetchall()
                        for emb_id, embedding_id, segment_id in embeddings:
                            logger.info(f"  向量: ID={emb_id}, embedding_id={embedding_id}, segment_id={segment_id}")
                    
                    elif table_name == 'embedding_metadata' and count > 0:
                        cursor.execute("SELECT id, key, string_value FROM embedding_metadata LIMIT 10;")
                        metadata = cursor.fetchall()
                        for meta_id, key, value in metadata:
                            logger.info(f"  元数据: ID={meta_id}, key={key}, value={value}")
                            
                except Exception as e:
                    logger.error(f"❌ 查询表 {table_name} 失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调试SQLite数据库失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def debug_file_system():
    """调试文件系统状态"""
    try:
        from backend.config import settings
        
        logger.info("\n" + "=" * 60)
        logger.info("开始调试文件系统状态")
        logger.info("=" * 60)
        
        # 检查data目录
        data_path = Path(settings.data_dir)
        logger.info(f"📁 数据目录: {data_path.absolute()}")
        logger.info(f"📁 目录存在: {data_path.exists()}")
        
        if data_path.exists():
            txt_files = list(data_path.glob("*.txt"))
            logger.info(f"📄 TXT文件数量: {len(txt_files)}")
            
            for txt_file in txt_files:
                file_size = txt_file.stat().st_size
                logger.info(f"  文件: {txt_file.name}, 大小: {file_size} bytes")
                
                # 读取文件内容预览
                try:
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        preview = content[:100] + "..." if len(content) > 100 else content
                        logger.info(f"    内容预览: {preview}")
                except Exception as e:
                    logger.error(f"    ❌ 读取文件失败: {e}")
        
        # 检查storage目录
        storage_path = Path(settings.chroma_persist_directory)
        logger.info(f"\n📁 存储目录: {storage_path.absolute()}")
        logger.info(f"📁 目录存在: {storage_path.exists()}")
        
        if storage_path.exists():
            all_files = list(storage_path.rglob("*"))
            logger.info(f"📄 存储文件总数: {len([f for f in all_files if f.is_file()])}")
            
            for file_path in all_files:
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    logger.info(f"  文件: {file_path.name}, 大小: {file_size} bytes")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 调试文件系统失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("🔍 开始全面调试ChromaDB数据和文档管理问题")
    
    # 步骤1: 调试ChromaDB集合数据
    chroma_success = debug_chroma_collection()
    
    # 步骤2: 调试SQLite数据库数据
    sqlite_success = debug_sqlite_data()
    
    # 步骤3: 调试文件系统状态
    fs_success = debug_file_system()
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("调试结果总结")
    logger.info("=" * 60)
    logger.info(f"ChromaDB集合调试: {'✅ 成功' if chroma_success else '❌ 失败'}")
    logger.info(f"SQLite数据库调试: {'✅ 成功' if sqlite_success else '❌ 失败'}")
    logger.info(f"文件系统调试: {'✅ 成功' if fs_success else '❌ 失败'}")
    
    if chroma_success and sqlite_success and fs_success:
        logger.info("🎉 调试完成，请查看上述详细信息来诊断文档管理页面问题")
    else:
        logger.error("❌ 调试过程中发现问题，请检查错误信息")
    
    return chroma_success and sqlite_success and fs_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
