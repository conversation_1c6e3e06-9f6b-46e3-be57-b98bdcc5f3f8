#!/usr/bin/env python3
"""
分析embedding_metadata缺失导致的文档管理问题
"""
import os
import sys
import json
import sqlite3
import logging
from pathlib import Path
from typing import Dict, Any, List

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_data_structure():
    """分析ChromaDB数据结构和文档计数机制"""
    try:
        import chromadb
        from backend.config import settings
        
        logger.info("=" * 60)
        logger.info("分析ChromaDB数据结构和文档计数机制")
        logger.info("=" * 60)
        
        # 连接数据库
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.error("❌ 未找到SQLite数据库文件")
            return False
            
        db_file = sqlite_files[0]
        logger.info(f"📁 分析数据库: {db_file}")
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 1. 分析embeddings表
            logger.info("\n🔍 分析embeddings表（向量数据）:")
            cursor.execute("SELECT id, embedding_id, segment_id, created_at FROM embeddings;")
            embeddings = cursor.fetchall()
            
            logger.info(f"📊 embeddings表总记录数: {len(embeddings)}")
            for emb_id, embedding_id, segment_id, created_at in embeddings:
                logger.info(f"  ID: {emb_id}, embedding_id: {embedding_id}, segment_id: {segment_id}")
            
            # 2. 分析embedding_metadata表
            logger.info("\n🔍 分析embedding_metadata表（文档元数据）:")
            cursor.execute("SELECT COUNT(*) FROM embedding_metadata;")
            metadata_count = cursor.fetchone()[0]
            logger.info(f"📊 embedding_metadata表总记录数: {metadata_count}")
            
            if metadata_count > 0:
                cursor.execute("SELECT id, key, string_value FROM embedding_metadata;")
                metadata = cursor.fetchall()
                for meta_id, key, value in metadata:
                    logger.info(f"  ID: {meta_id}, key: {key}, value: {value}")
            else:
                logger.warning("⚠️ embedding_metadata表为空！这就是问题所在！")
            
            # 3. 分析文档计数逻辑
            logger.info("\n🔍 分析文档计数逻辑:")
            logger.info("ChromaDB文档计数机制:")
            logger.info("1. embeddings表存储向量数据")
            logger.info("2. embedding_metadata表存储文档元数据（包括filename）")
            logger.info("3. 文档管理页面通过embedding_metadata表的filename字段统计文档")
            logger.info("4. 如果embedding_metadata为空，就无法知道有哪些文档")
            
            # 4. 检查数据一致性
            logger.info("\n🔍 检查数据一致性:")
            logger.info(f"向量数据: {len(embeddings)} 条")
            logger.info(f"元数据: {metadata_count} 条")
            
            if len(embeddings) > 0 and metadata_count == 0:
                logger.error("❌ 数据不一致：有向量数据但无元数据")
                logger.error("这会导致：")
                logger.error("  - 文档管理页面显示0个文档")
                logger.error("  - 无法获取文档列表")
                logger.error("  - 无法知道文档的原始文件名")
                logger.error("  - 查询功能可能正常（因为向量还在）")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def analyze_api_behavior():
    """分析API如何获取文档列表"""
    try:
        logger.info("\n" + "=" * 60)
        logger.info("分析API获取文档列表的机制")
        logger.info("=" * 60)
        
        # 查看RAG服务的get_documents方法
        from backend.app.rag_service import RAGService
        
        logger.info("📋 RAG服务获取文档列表的逻辑:")
        logger.info("1. 调用collection.get()获取所有数据")
        logger.info("2. 从metadatas中提取filename字段")
        logger.info("3. 按filename分组统计文档块数")
        logger.info("4. 如果metadatas为空，返回空列表")
        
        # 测试实际API调用
        logger.info("\n🧪 测试实际API调用:")
        rag_service = RAGService()
        documents = rag_service.get_documents()
        
        logger.info(f"API返回的文档数量: {len(documents)}")
        if documents:
            for doc in documents:
                logger.info(f"  文档: {doc}")
        else:
            logger.warning("⚠️ API返回空文档列表（证实了问题）")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ API分析失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def propose_solution():
    """提出解决方案"""
    logger.info("\n" + "=" * 60)
    logger.info("问题诊断和解决方案")
    logger.info("=" * 60)
    
    logger.info("🔍 问题诊断:")
    logger.info("1. ❌ embedding_metadata表被清空")
    logger.info("2. ❌ 向量数据还在，但失去了文档元数据")
    logger.info("3. ❌ 文档管理页面无法获取文档列表")
    logger.info("4. ❌ 无法知道原始文件名和其他元数据")
    
    logger.info("\n💡 解决方案选项:")
    logger.info("方案1: 清理所有数据，重新开始")
    logger.info("  - 删除所有向量数据")
    logger.info("  - 重新上传文档")
    logger.info("  - 优点: 干净彻底")
    logger.info("  - 缺点: 丢失所有数据")
    
    logger.info("\n方案2: 尝试恢复元数据（如果有备份）")
    logger.info("  - 从备份恢复embedding_metadata表")
    logger.info("  - 优点: 保留现有数据")
    logger.info("  - 缺点: 需要有备份")
    
    logger.info("\n方案3: 重建元数据（推荐）")
    logger.info("  - 保留向量数据")
    logger.info("  - 为现有向量重新生成元数据")
    logger.info("  - 使用默认文件名")
    logger.info("  - 优点: 不丢失向量数据")
    logger.info("  - 缺点: 丢失原始文件名")
    
    logger.info("\n🎯 推荐方案: 清理重建（方案1）")
    logger.info("理由:")
    logger.info("1. 数据量小，重新上传成本低")
    logger.info("2. 确保数据一致性")
    logger.info("3. 避免潜在的数据问题")
    logger.info("4. 重新开始更安全可靠")

def main():
    """主函数"""
    logger.info("🔍 开始分析embedding_metadata缺失问题")
    
    # 分析数据结构
    structure_success = analyze_data_structure()
    
    # 分析API行为
    api_success = analyze_api_behavior()
    
    # 提出解决方案
    propose_solution()
    
    return structure_success and api_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
