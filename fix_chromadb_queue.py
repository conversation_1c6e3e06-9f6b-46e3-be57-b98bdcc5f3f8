#!/usr/bin/env python3
"""
修复ChromaDB队列问题的脚本
"""
import sqlite3
import json
import chromadb
from pathlib import Path
import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.config import settings

def fix_chromadb_queue():
    """修复ChromaDB队列中卡住的数据"""
    try:
        print("=== 修复ChromaDB队列问题 ===")
        
        db_path = Path("storage/chroma.sqlite3")
        if not db_path.exists():
            print(f"数据库文件不存在: {db_path}")
            return
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查队列状态
        cursor.execute("SELECT COUNT(*) FROM embeddings_queue;")
        queue_count = cursor.fetchone()[0]
        print(f"队列中的记录数: {queue_count}")
        
        cursor.execute("SELECT COUNT(*) FROM embeddings;")
        embeddings_count = cursor.fetchone()[0]
        print(f"embeddings表中的记录数: {embeddings_count}")
        
        cursor.execute("SELECT COUNT(*) FROM embedding_metadata;")
        metadata_count = cursor.fetchone()[0]
        print(f"embedding_metadata表中的记录数: {metadata_count}")
        
        if queue_count == 0:
            print("队列为空，无需修复")
            conn.close()
            return
        
        print(f"\n发现队列中有 {queue_count} 条记录需要处理")
        
        # 方案1：清空队列，重新初始化ChromaDB
        print("\n选择修复方案：")
        print("1. 清空队列和所有数据，重新初始化（推荐）")
        print("2. 尝试手动处理队列数据")
        print("3. 仅清空队列，保留现有数据")
        
        choice = input("请选择方案 (1/2/3): ").strip()
        
        if choice == "1":
            # 清空所有相关表
            print("\n清空所有ChromaDB数据...")
            tables_to_clear = [
                "embeddings_queue",
                "embeddings", 
                "embedding_metadata",
                "embedding_fulltext_search",
                "embedding_fulltext_search_data",
                "embedding_fulltext_search_idx",
                "embedding_fulltext_search_content",
                "embedding_fulltext_search_docsize"
            ]
            
            for table in tables_to_clear:
                try:
                    cursor.execute(f"DELETE FROM {table};")
                    print(f"已清空表: {table}")
                except Exception as e:
                    print(f"清空表 {table} 失败: {e}")
            
            conn.commit()
            print("数据清空完成")
            
        elif choice == "2":
            print("\n手动处理队列数据功能暂未实现")
            print("建议选择方案1进行完全重置")
            
        elif choice == "3":
            # 仅清空队列
            print("\n清空队列...")
            cursor.execute("DELETE FROM embeddings_queue;")
            conn.commit()
            print("队列已清空")
        
        else:
            print("无效选择，退出")
            conn.close()
            return
        
        conn.close()
        
        # 如果选择了方案1，重新初始化ChromaDB
        if choice == "1":
            print("\n重新初始化ChromaDB...")
            try:
                # 删除向量存储目录
                import shutil
                vector_dirs = [
                    "storage/7e06c642-fe77-40ed-8a8b-898d71e6d5e8",
                    "storage/9dadb9f5-24b4-485e-9821-9c3289427a45", 
                    "storage/cf0c7e6c-a894-4a85-9fef-286e32b30c47"
                ]
                
                for vector_dir in vector_dirs:
                    if os.path.exists(vector_dir):
                        shutil.rmtree(vector_dir)
                        print(f"已删除向量目录: {vector_dir}")
                
                # 重新创建集合
                client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
                
                # 删除旧集合（如果存在）
                try:
                    client.delete_collection(name=settings.collection_name)
                    print("已删除旧集合")
                except Exception:
                    pass
                
                # 创建新集合
                collection = client.create_collection(
                    name=settings.collection_name,
                    metadata={"hnsw:space": "cosine"}
                )
                print(f"已创建新集合: {settings.collection_name}")
                
                print("ChromaDB重新初始化完成")
                
            except Exception as e:
                print(f"重新初始化ChromaDB失败: {e}")
        
        print("\n修复完成！现在可以重新上传文档了。")
        
    except Exception as e:
        print(f"修复失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_chromadb_queue()
