#!/usr/bin/env python3
"""
测试数据库功能脚本
验证ChromaDB和SQLite的基本功能
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_document_upload():
    """测试文档上传和向量化功能"""
    try:
        import chromadb
        from backend.config import settings
        
        logger.info("测试文档上传功能...")
        
        # 创建测试文档
        test_content = """
        这是一个测试文档，用于验证ChromaDB和SQLite数据库的功能。
        
        主要测试内容包括：
        1. 文档向量化
        2. 向量存储
        3. 元数据存储
        4. 全文检索
        
        ChromaDB是一个开源的向量数据库，专门用于存储和检索向量嵌入。
        它使用SQLite作为底层存储，提供了高效的向量相似性搜索功能。
        """
        
        # 连接ChromaDB
        client = chromadb.PersistentClient(path=settings.chroma_persist_directory)
        collection = client.get_collection(name=settings.collection_name)
        
        # 添加测试文档
        collection.add(
            documents=[test_content],
            metadatas=[{
                "filename": "test_document.txt",
                "file_path": "./data/test_document.txt",
                "file_size": len(test_content),
                "content_type": "text/plain"
            }],
            ids=["test_doc_1"]
        )
        
        logger.info("✅ 测试文档添加成功")
        
        # 验证文档数量
        count = collection.count()
        logger.info(f"集合中的文档数量: {count}")
        
        # 测试查询
        results = collection.query(
            query_texts=["ChromaDB向量数据库"],
            n_results=1
        )
        
        if results['documents']:
            logger.info("✅ 向量检索测试成功")
            logger.info(f"检索到的文档片段: {results['documents'][0][0][:100]}...")
        else:
            logger.warning("❌ 向量检索测试失败")
            
        return True
        
    except Exception as e:
        logger.error(f"文档上传测试失败: {e}")
        return False

def test_sqlite_data():
    """测试SQLite数据存储"""
    try:
        import sqlite3
        from backend.config import settings
        
        logger.info("测试SQLite数据存储...")
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.error("未找到SQLite数据库文件")
            return False
            
        db_file = sqlite_files[0]
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查embeddings表
            cursor.execute("SELECT COUNT(*) FROM embeddings;")
            embedding_count = cursor.fetchone()[0]
            logger.info(f"embeddings表记录数: {embedding_count}")
            
            # 检查embedding_metadata表
            cursor.execute("SELECT COUNT(*) FROM embedding_metadata;")
            metadata_count = cursor.fetchone()[0]
            logger.info(f"embedding_metadata表记录数: {metadata_count}")
            
            # 检查collections表
            cursor.execute("SELECT name, dimension FROM collections;")
            collections = cursor.fetchall()
            for name, dimension in collections:
                logger.info(f"集合: {name}, 维度: {dimension}")
                
            if embedding_count > 0:
                logger.info("✅ SQLite数据存储测试成功")
                return True
            else:
                logger.warning("⚠️ SQLite中暂无向量数据")
                return True
                
    except Exception as e:
        logger.error(f"SQLite数据测试失败: {e}")
        return False

def test_fulltext_search():
    """测试全文检索功能"""
    try:
        import sqlite3
        from backend.config import settings
        
        logger.info("测试全文检索功能...")
        
        storage_path = Path(settings.chroma_persist_directory)
        sqlite_files = list(storage_path.glob("*.sqlite3"))
        
        if not sqlite_files:
            logger.error("未找到SQLite数据库文件")
            return False
            
        db_file = sqlite_files[0]
        
        with sqlite3.connect(db_file) as conn:
            cursor = conn.cursor()
            
            # 检查全文检索表
            cursor.execute("SELECT COUNT(*) FROM embedding_fulltext_search;")
            fts_count = cursor.fetchone()[0]
            logger.info(f"全文检索表记录数: {fts_count}")
            
            if fts_count > 0:
                # 测试全文检索
                cursor.execute("""
                    SELECT string_value FROM embedding_fulltext_search 
                    WHERE embedding_fulltext_search MATCH '向量' 
                    LIMIT 1;
                """)
                result = cursor.fetchone()
                
                if result:
                    logger.info("✅ 全文检索测试成功")
                    logger.info(f"检索结果: {result[0][:100]}...")
                else:
                    logger.info("⚠️ 全文检索无匹配结果")
                    
            return True
                
    except Exception as e:
        logger.error(f"全文检索测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始数据库功能测试")
    logger.info("=" * 50)
    
    # 测试1: 文档上传
    logger.info("\n测试1: 文档上传和向量化")
    upload_success = test_document_upload()
    
    # 测试2: SQLite数据
    logger.info("\n测试2: SQLite数据存储")
    sqlite_success = test_sqlite_data()
    
    # 测试3: 全文检索
    logger.info("\n测试3: 全文检索功能")
    fts_success = test_fulltext_search()
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("功能测试结果总结:")
    logger.info(f"  文档上传测试: {'✅ 成功' if upload_success else '❌ 失败'}")
    logger.info(f"  SQLite数据测试: {'✅ 成功' if sqlite_success else '❌ 失败'}")
    logger.info(f"  全文检索测试: {'✅ 成功' if fts_success else '❌ 失败'}")
    
    if upload_success and sqlite_success and fts_success:
        logger.info("🎉 所有功能测试通过!")
        return True
    else:
        logger.error("❌ 部分功能测试失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
