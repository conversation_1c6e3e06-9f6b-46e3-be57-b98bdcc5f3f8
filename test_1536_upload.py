#!/usr/bin/env python3
"""
测试1536维向量上传功能
"""
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "backend"))

def test_real_embedding():
    """测试真实的OpenAI嵌入"""
    try:
        from backend.app.rag_service import RAGService
        
        print("初始化RAG服务...")
        rag_service = RAGService()
        
        print("测试文档上传...")
        
        # 创建测试文档
        test_file = Path("data/test_1536_dim.txt")
        test_file.parent.mkdir(exist_ok=True)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("""
这是一个测试文档，用于验证1536维向量功能。

测试内容：
1. OpenAI text-embedding-3-small模型
2. 1536维向量生成
3. ChromaDB存储
4. 向量检索功能

这个测试将验证整个RAG系统是否能正确处理1536维的向量数据。
            """)
        
        print(f"测试文件已创建: {test_file}")
        
        # 读取文件内容
        with open(test_file, 'r', encoding='utf-8') as f:
            file_content = f.read()

        # 上传文档（正确的参数）
        result = rag_service.upload_document(
            file_content=file_content,
            filename="test_1536_dim.txt"
        )

        if result["success"]:
            print("✅ 文档上传成功!")
            print(f"新文档块数: {result.get('new_chunks', 0)}")
            print(f"总文档块数: {result.get('total_chunks', 0)}")

            # 测试查询
            query_result = rag_service.query("1536维向量", max_results=1)
            if query_result and query_result.get("success") and query_result.get("sources"):
                print("✅ 查询测试成功!")
                print(f"检索到 {len(query_result['sources'])} 个结果")
                print(f"答案: {query_result.get('answer', '')[:100]}...")
            else:
                print("⚠️ 查询测试无结果")

        else:
            print(f"❌ 文档上传失败: {result.get('message', '未知错误')}")
            
        return result["success"]
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_real_embedding()
    print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
