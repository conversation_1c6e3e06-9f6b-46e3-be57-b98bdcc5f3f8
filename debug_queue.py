#!/usr/bin/env python3
"""
调试ChromaDB队列数据的脚本
"""
import sqlite3
import json
from pathlib import Path

def debug_queue():
    """调试ChromaDB的队列数据"""
    try:
        db_path = Path("storage/chroma.sqlite3")
        if not db_path.exists():
            print(f"数据库文件不存在: {db_path}")
            return
        
        print(f"=== ChromaDB 队列数据调试 ===")
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查embeddings_queue表结构
        print(f"\n=== embeddings_queue 表结构 ===")
        cursor.execute("PRAGMA table_info(embeddings_queue);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查队列中的数据
        print(f"\n=== embeddings_queue 数据样本 ===")
        cursor.execute("SELECT * FROM embeddings_queue LIMIT 5;")
        rows = cursor.fetchall()
        
        for i, row in enumerate(rows):
            print(f"\n--- 队列记录 {i+1} ---")
            for j, value in enumerate(row):
                col_name = columns[j][1] if j < len(columns) else f"col_{j}"
                print(f"{col_name}: {value}")
                
                # 如果是JSON字符串，尝试解析
                if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                    try:
                        parsed = json.loads(value)
                        print(f"  解析JSON: {json.dumps(parsed, indent=2, ensure_ascii=False)}")
                        
                        # 检查文件名编码
                        if isinstance(parsed, dict):
                            for key, val in parsed.items():
                                if 'filename' in key.lower() or 'file_name' in key.lower():
                                    print(f"  文件名字段 {key}: {val}")
                                    if isinstance(val, str) and "\\u" in val:
                                        try:
                                            decoded = val.encode().decode('unicode_escape')
                                            print(f"  解码后: {decoded}")
                                        except Exception as e:
                                            print(f"  解码失败: {e}")
                        elif isinstance(parsed, list):
                            for item in parsed[:3]:  # 只显示前3个
                                if isinstance(item, dict):
                                    for key, val in item.items():
                                        if 'filename' in key.lower() or 'file_name' in key.lower():
                                            print(f"  列表项文件名字段 {key}: {val}")
                                            if isinstance(val, str) and "\\u" in val:
                                                try:
                                                    decoded = val.encode().decode('unicode_escape')
                                                    print(f"  解码后: {decoded}")
                                                except Exception as e:
                                                    print(f"  解码失败: {e}")
                    except json.JSONDecodeError:
                        if len(str(value)) > 100:
                            print(f"  长字符串: {str(value)[:100]}...")
                        else:
                            print(f"  字符串: {value}")
        
        # 统计队列中的操作类型
        print(f"\n=== 队列操作统计 ===")
        cursor.execute("SELECT operation, COUNT(*) FROM embeddings_queue GROUP BY operation;")
        operations = cursor.fetchall()
        for op, count in operations:
            print(f"{op}: {count} 条")
        
        # 检查是否有错误状态
        cursor.execute("SELECT DISTINCT status FROM embeddings_queue;")
        statuses = cursor.fetchall()
        print(f"\n队列状态: {[status[0] for status in statuses]}")
        
        conn.close()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_queue()
