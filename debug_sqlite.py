#!/usr/bin/env python3
"""
调试ChromaDB SQLite数据库的脚本
"""
import sqlite3
import json
from pathlib import Path

def debug_sqlite():
    """调试ChromaDB的SQLite数据库"""
    try:
        db_path = Path("storage/chroma.sqlite3")
        if not db_path.exists():
            print(f"数据库文件不存在: {db_path}")
            return
        
        print(f"=== ChromaDB SQLite 数据库调试 ===")
        print(f"数据库路径: {db_path}")
        print(f"数据库大小: {db_path.stat().st_size / 1024 / 1024:.2f} MB")
        
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"\n数据库表: {[table[0] for table in tables]}")
        
        # 检查embedding_metadata表
        if any('embedding_metadata' in table for table in tables):
            print(f"\n=== embedding_metadata 表结构 ===")
            cursor.execute("PRAGMA table_info(embedding_metadata);")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            print(f"\n=== embedding_metadata 数据样本 ===")
            cursor.execute("SELECT * FROM embedding_metadata LIMIT 10;")
            rows = cursor.fetchall()
            for i, row in enumerate(rows):
                print(f"行 {i+1}: {row}")
                
                # 如果有string_value字段，检查是否包含文件名信息
                if len(row) > 2:  # 假设第3列是string_value
                    string_value = row[2]
                    if string_value and isinstance(string_value, str):
                        # 检查是否是JSON
                        if string_value.startswith('{'):
                            try:
                                parsed = json.loads(string_value)
                                print(f"  解析JSON: {parsed}")
                                
                                # 检查文件名编码
                                if isinstance(parsed, dict):
                                    for key, value in parsed.items():
                                        if 'filename' in key.lower() or 'file_name' in key.lower():
                                            print(f"  文件名字段 {key}: {value}")
                                            if isinstance(value, str) and "\\u" in value:
                                                try:
                                                    decoded = value.encode().decode('unicode_escape')
                                                    print(f"  解码后: {decoded}")
                                                except Exception as e:
                                                    print(f"  解码失败: {e}")
                            except json.JSONDecodeError:
                                print(f"  非JSON字符串: {string_value[:100]}...")
                        else:
                            print(f"  字符串值: {string_value[:100]}...")
        
        # 检查collections表
        if any('collections' in table for table in tables):
            print(f"\n=== collections 表 ===")
            cursor.execute("SELECT * FROM collections;")
            collections = cursor.fetchall()
            for collection in collections:
                print(f"集合: {collection}")
        
        # 检查segments表
        if any('segments' in table for table in tables):
            print(f"\n=== segments 表 ===")
            cursor.execute("SELECT * FROM segments;")
            segments = cursor.fetchall()
            for segment in segments:
                print(f"分段: {segment}")
        
        # 统计各表的记录数
        print(f"\n=== 各表记录数统计 ===")
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"{table_name}: {count} 条记录")
            except Exception as e:
                print(f"{table_name}: 查询失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_sqlite()
