"""
CMS API路由
"""
import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime
import math

from .database import get_db_session, db_manager
from .service import cms_service
from .models import (
    ArticleResponse, ArticleListResponse, ArticleCreate, ArticleUpdate,
    SyncRequest, SyncResponse, DatabaseStatus, HealthResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/cms", tags=["CMS"])

def get_db():
    """获取数据库会话依赖"""
    db = get_db_session()
    if db is None:
        raise HTTPException(status_code=503, detail="数据库服务不可用")
    try:
        yield db
    finally:
        db.close()

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    db_connected = db_manager.is_connected()
    article_count = None
    
    if db_connected:
        try:
            db = get_db_session()
            if db:
                from .database import Article
                article_count = db.query(Article).filter(Article.is_deleted == False).count()
                db.close()
        except Exception as e:
            logger.error(f"获取文章数量失败: {e}")
            db_connected = False
    
    database_status = DatabaseStatus(
        connected=db_connected,
        message="数据库连接正常" if db_connected else "数据库连接失败",
        article_count=article_count
    )
    
    return HealthResponse(
        status="healthy" if db_connected else "degraded",
        database=database_status,
        timestamp=datetime.utcnow()
    )

@router.get("/articles", response_model=ArticleListResponse)
async def get_articles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category: Optional[str] = Query(None, description="分类筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db)
):
    """获取文章列表"""
    try:
        articles, total = cms_service.get_articles(
            db=db,
            page=page,
            page_size=page_size,
            search=search,
            category=category,
            status=status
        )
        
        total_pages = math.ceil(total / page_size) if total > 0 else 0
        
        return ArticleListResponse(
            articles=[ArticleResponse.from_orm(article) for article in articles],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"获取文章列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取文章列表失败")

@router.get("/articles/{article_id}", response_model=ArticleResponse)
async def get_article(article_id: int, db: Session = Depends(get_db)):
    """获取单个文章"""
    article = cms_service.get_article_by_id(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    return ArticleResponse.from_orm(article)

@router.post("/articles", response_model=ArticleResponse)
async def create_article(article_data: ArticleCreate, db: Session = Depends(get_db)):
    """创建文章"""
    try:
        article = cms_service.create_article(db, article_data)
        return ArticleResponse.from_orm(article)
    except Exception as e:
        logger.error(f"创建文章失败: {e}")
        raise HTTPException(status_code=500, detail="创建文章失败")

@router.put("/articles/{article_id}", response_model=ArticleResponse)
async def update_article(
    article_id: int, 
    article_data: ArticleUpdate, 
    db: Session = Depends(get_db)
):
    """更新文章"""
    try:
        article = cms_service.update_article(db, article_id, article_data)
        if not article:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        return ArticleResponse.from_orm(article)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新文章失败: {e}")
        raise HTTPException(status_code=500, detail="更新文章失败")

@router.delete("/articles/{article_id}")
async def delete_article(article_id: int, db: Session = Depends(get_db)):
    """删除文章"""
    try:
        success = cms_service.delete_article(db, article_id)
        if not success:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        return {"message": "文章删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文章失败: {e}")
        raise HTTPException(status_code=500, detail="删除文章失败")

@router.get("/articles-to-add")
async def get_articles_to_add(db: Session = Depends(get_db)):
    """获取需要添加到RAG的文章"""
    try:
        articles = cms_service.get_articles_to_add(db)
        return {
            "articles": articles,
            "count": len(articles)
        }
    except Exception as e:
        logger.error(f"获取待添加文章失败: {e}")
        raise HTTPException(status_code=500, detail="获取待添加文章失败")

@router.get("/articles-to-delete")
async def get_articles_to_delete(db: Session = Depends(get_db)):
    """获取需要从RAG删除的文档"""
    try:
        documents = cms_service.get_articles_to_delete(db)
        return {
            "documents": documents,
            "count": len(documents)
        }
    except Exception as e:
        logger.error(f"获取待删除文档失败: {e}")
        raise HTTPException(status_code=500, detail="获取待删除文档失败")

@router.post("/sync", response_model=SyncResponse)
async def sync_articles(sync_request: SyncRequest, db: Session = Depends(get_db)):
    """同步文章到RAG系统"""
    try:
        result = cms_service.sync_articles(db, sync_request.force)
        return result
    except Exception as e:
        logger.error(f"同步文章失败: {e}")
        raise HTTPException(status_code=500, detail="同步文章失败")

@router.post("/convert-html")
async def convert_html_to_text(html_content: str):
    """将HTML内容转换为纯文本"""
    try:
        text_content = cms_service.html_to_text(html_content)
        return {
            "text_content": text_content,
            "length": len(text_content)
        }
    except Exception as e:
        logger.error(f"HTML转换失败: {e}")
        raise HTTPException(status_code=500, detail="HTML转换失败")
